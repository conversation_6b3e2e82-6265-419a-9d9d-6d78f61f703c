<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排行榜效果测试</title>
    <link href="lib/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/space.css">
</head>
<body class="space-background">

    


    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card space-card">
                    <div class="card-header space-card-header">
                        <h3 class="space-title text-center mb-0">排行榜效果演示</h3>
                    </div>
                    <div class="card-body">
                        <button id="update-btn" class="btn btn-primary space-btn-primary mb-3">更新排行榜</button>
                        <button id="new-record-btn" class="btn btn-success space-btn-primary mb-3 ms-2">新记录</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="leaderboard-container">
                    <div class="card space-card">
                        <div class="card-header space-card-header">
                            <h5 class="mb-0">🚀 宇宙排行榜（前十）</h5>
                        </div>
                        <div class="card-body p-0">
                            <table class="table table-dark table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>宇航员</th>
                                        <th>得分</th>
                                    </tr>
                                </thead>
                                <tbody id="test-leaderboard">
                                    <!-- 测试数据 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试数据
        let testData = [
            { nickname: "星际探索者001", score: 1250 },
            { nickname: "银河战士", score: 1180 },
            { nickname: "太空飞行员", score: 1120 },
            { nickname: "宇宙守护者", score: 980 },
            { nickname: "星云旅行者", score: 920 },
            { nickname: "彗星猎人", score: 850 },
            { nickname: "行星探险家", score: 780 },
            { nickname: "火箭工程师", score: 720 },
            { nickname: "太空科学家", score: 650 },
            { nickname: "宇航员新手", score: 580 }
        ];


        


        // 更新排行榜函数
        function updateTestLeaderboard(leaderboard) {
            const leaderboardBody = document.getElementById('test-leaderboard');
            if (!leaderboardBody) return;

            leaderboardBody.innerHTML = '';

            leaderboard.forEach((entry, index) => {
                const row = document.createElement('tr');
                const rank = index + 1;

                // 为前三名添加特殊样式类
                if (rank === 1) {
                    row.classList.add('leaderboard-rank-1');
                } else if (rank === 2) {
                    row.classList.add('leaderboard-rank-2');
                } else if (rank === 3) {
                    row.classList.add('leaderboard-rank-3');
                }

                row.innerHTML = `
                    <td>${rank}</td>
                    <td class="leaderboard-nickname">${entry.nickname}</td>
                    <td>${entry.score}</td>
                `;
                leaderboardBody.appendChild(row);
            });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTestLeaderboard(testData);

            // 更新按钮事件
            document.getElementById('update-btn').addEventListener('click', function() {
                // 随机改变分数
                testData.forEach(entry => {
                    entry.score += Math.floor(Math.random() * 100) - 50;
                    entry.score = Math.max(0, entry.score);
                });
                testData.sort((a, b) => b.score - a.score);
                updateTestLeaderboard(testData);
            });

            // 新记录按钮事件
            document.getElementById('new-record-btn').addEventListener('click', function() {
                testData[0].score += 200;
                testData.sort((a, b) => b.score - a.score);
                updateTestLeaderboard(testData);
            });
        });
    </script>
</body>
</html>
