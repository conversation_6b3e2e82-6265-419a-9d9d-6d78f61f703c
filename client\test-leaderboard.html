<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排行榜效果测试</title>
    <link href="lib/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/space.css">
</head>
<body class="space-background">
    <div class="stars"></div>
    <div class="twinkling"></div>
    <div class="clouds"></div>
    
    <!-- 科技感粒子效果 -->
    <div class="particles" id="particles"></div>
    
    <!-- 科技感光线效果 -->
    <div class="tech-lines">
        <div class="tech-line"></div>
        <div class="tech-line"></div>
        <div class="tech-line"></div>
    </div>
    
    <!-- 脉冲光环效果 -->
    <div class="pulse-rings">
        <div class="pulse-ring"></div>
        <div class="pulse-ring"></div>
        <div class="pulse-ring"></div>
    </div>
    
    <!-- 数据流效果 -->
    <div class="data-streams" id="data-streams"></div>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card space-card">
                    <div class="card-header space-card-header">
                        <h3 class="space-title text-center mb-0">排行榜效果演示</h3>
                    </div>
                    <div class="card-body">
                        <button id="update-btn" class="btn btn-primary space-btn-primary mb-3">更新排行榜</button>
                        <button id="new-record-btn" class="btn btn-success space-btn-primary mb-3 ms-2">新记录</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="leaderboard-container">
                    <div class="card space-card">
                        <div class="card-header space-card-header">
                            <h5 class="mb-0">🚀 宇宙排行榜（前十）</h5>
                        </div>
                        <div class="card-body p-0">
                            <table class="table table-dark table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>宇航员</th>
                                        <th>得分</th>
                                    </tr>
                                </thead>
                                <tbody id="test-leaderboard">
                                    <!-- 测试数据 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试数据
        let testData = [
            { nickname: "星际探索者001", score: 1250 },
            { nickname: "银河战士", score: 1180 },
            { nickname: "太空飞行员", score: 1120 },
            { nickname: "宇宙守护者", score: 980 },
            { nickname: "星云旅行者", score: 920 },
            { nickname: "彗星猎人", score: 850 },
            { nickname: "行星探险家", score: 780 },
            { nickname: "火箭工程师", score: 720 },
            { nickname: "太空科学家", score: 650 },
            { nickname: "宇航员新手", score: 580 }
        ];

        // 创建粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            if (!particlesContainer) return;
            
            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (6 + Math.random() * 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }
        
        // 创建数据流效果
        function createDataStreams() {
            const streamsContainer = document.getElementById('data-streams');
            if (!streamsContainer) return;
            
            for (let i = 0; i < 20; i++) {
                const stream = document.createElement('div');
                stream.className = 'data-stream';
                stream.style.left = Math.random() * 100 + '%';
                stream.style.animationDelay = Math.random() * 5 + 's';
                stream.style.animationDuration = (3 + Math.random() * 2) + 's';
                streamsContainer.appendChild(stream);
            }
        }

        // 更新排行榜函数
        function updateTestLeaderboard(leaderboard) {
            const leaderboardBody = document.getElementById('test-leaderboard');
            if (!leaderboardBody) return;

            // 添加更新闪烁效果
            const leaderboardContainer = leaderboardBody.closest('.leaderboard-container');
            if (leaderboardContainer) {
                leaderboardContainer.classList.add('leaderboard-update-flash');
                
                // 添加数据传输效果
                const dataTransfer = document.createElement('div');
                dataTransfer.className = 'data-transfer';
                leaderboardContainer.style.position = 'relative';
                leaderboardContainer.appendChild(dataTransfer);
                
                setTimeout(() => {
                    leaderboardContainer.classList.remove('leaderboard-update-flash');
                    if (dataTransfer.parentNode) {
                        dataTransfer.parentNode.removeChild(dataTransfer);
                    }
                }, 1500);
            }

            // 添加加载动画
            leaderboardBody.classList.add('leaderboard-loading');
            
            // 清空现有内容
            leaderboardBody.innerHTML = '';

            // 延迟添加新内容
            setTimeout(() => {
                leaderboard.forEach((entry, index) => {
                    const row = document.createElement('tr');
                    const rank = index + 1;
                    
                    // 添加行进入动画
                    row.classList.add('leaderboard-row-enter');
                    row.style.animationDelay = `${index * 0.1}s`;
                    
                    // 为前三名添加特殊样式类
                    if (rank === 1) {
                        row.classList.add('leaderboard-rank-1');
                    } else if (rank === 2) {
                        row.classList.add('leaderboard-rank-2');
                    } else if (rank === 3) {
                        row.classList.add('leaderboard-rank-3');
                    }
                    
                    // 创建得分单元格并添加跳动效果
                    const scoreCell = document.createElement('td');
                    scoreCell.className = 'score-animate score-jump';
                    scoreCell.textContent = entry.score;
                    
                    row.innerHTML = `
                        <td>${rank}</td>
                        <td class="leaderboard-nickname">${entry.nickname}</td>
                    `;
                    row.appendChild(scoreCell);
                    leaderboardBody.appendChild(row);
                });
                
                // 移除加载动画
                leaderboardBody.classList.remove('leaderboard-loading');
                
                // 清理动画类
                setTimeout(() => {
                    const rows = leaderboardBody.querySelectorAll('tr');
                    rows.forEach(row => {
                        row.classList.remove('leaderboard-row-enter', 'score-jump');
                    });
                }, 2000);
            }, 300);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            createDataStreams();
            updateTestLeaderboard(testData);

            // 更新按钮事件
            document.getElementById('update-btn').addEventListener('click', function() {
                // 随机改变分数
                testData.forEach(entry => {
                    entry.score += Math.floor(Math.random() * 100) - 50;
                    entry.score = Math.max(0, entry.score);
                });
                testData.sort((a, b) => b.score - a.score);
                updateTestLeaderboard(testData);
            });

            // 新记录按钮事件
            document.getElementById('new-record-btn').addEventListener('click', function() {
                testData[0].score += 200;
                testData.sort((a, b) => b.score - a.score);
                updateTestLeaderboard(testData);
            });
        });
    </script>
</body>
</html>
