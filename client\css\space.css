/* 太空科技感背景样式 */

/* 主背景类 */
.space-background {
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    background-attachment: fixed;
    position: relative;
    overflow-x: hidden;
}

/* 动态网格背景 */
.space-background::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(56, 128, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(56, 128, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -3;
    animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* 粒子效果 */
.particles {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    pointer-events: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(56, 128, 255, 0.8);
    border-radius: 50%;
    animation: float 6s linear infinite;
}

.particle:nth-child(odd) {
    background: rgba(255, 56, 128, 0.6);
    animation-duration: 8s;
}

.particle:nth-child(3n) {
    background: rgba(128, 255, 56, 0.4);
    animation-duration: 10s;
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* 科技感光线效果 */
.tech-lines {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
}

.tech-line {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(56, 128, 255, 0.8), transparent);
    animation: scan 4s linear infinite;
}

.tech-line:nth-child(1) {
    top: 20%;
    width: 100%;
    animation-delay: 0s;
}

.tech-line:nth-child(2) {
    top: 60%;
    width: 100%;
    animation-delay: 2s;
}

.tech-line:nth-child(3) {
    top: 80%;
    width: 100%;
    animation-delay: 1s;
}

@keyframes scan {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 脉冲光环效果 */
.pulse-rings {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    height: 300px;
    z-index: -2;
    pointer-events: none;
}

.pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid rgba(56, 128, 255, 0.3);
    border-radius: 50%;
    animation: pulse 4s ease-out infinite;
}

.pulse-ring:nth-child(1) {
    animation-delay: 0s;
}

.pulse-ring:nth-child(2) {
    animation-delay: 1s;
}

.pulse-ring:nth-child(3) {
    animation-delay: 2s;
}

@keyframes pulse {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

/* 数字雨效果 */
.digital-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    pointer-events: none;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: rgba(0, 255, 0, 0.3);
    overflow: hidden;
}

.rain-column {
    position: absolute;
    top: -100px;
    animation: rain-fall 8s linear infinite;
}

@keyframes rain-fall {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* 全息投影效果 */
.hologram-effect {
    position: relative;
}

.hologram-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(56, 128, 255, 0.1) 50%,
        transparent 100%
    );
    animation: hologram-scan 3s linear infinite;
    pointer-events: none;
}

@keyframes hologram-scan {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 能量波纹效果 */
.energy-ripple {
    position: absolute;
    border-radius: 50%;
    border: 2px solid rgba(56, 128, 255, 0.6);
    animation: ripple 2s ease-out infinite;
}

@keyframes ripple {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 200px;
        height: 200px;
        opacity: 0;
    }
}

/* 科技感按钮增强 */
.space-btn-enhanced {
    position: relative;
    overflow: hidden;
    background: linear-gradient(45deg, rgba(56, 128, 255, 0.2), rgba(56, 128, 255, 0.4));
    border: 1px solid rgba(56, 128, 255, 0.6);
    box-shadow: 0 0 20px rgba(56, 128, 255, 0.3);
    transition: all 0.3s ease;
}

.space-btn-enhanced:hover {
    box-shadow: 0 0 30px rgba(56, 128, 255, 0.6);
    transform: translateY(-2px);
}

.space-btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.space-btn-enhanced:hover::before {
    left: 100%;
}

/* 数据流效果 */
.data-streams {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    pointer-events: none;
    overflow: hidden;
}

.data-stream {
    position: absolute;
    width: 2px;
    height: 100px;
    background: linear-gradient(to bottom,
        transparent,
        rgba(0, 255, 255, 0.8),
        rgba(0, 255, 255, 0.4),
        transparent);
    animation: stream-flow 3s linear infinite;
}

.data-stream:nth-child(odd) {
    background: linear-gradient(to bottom,
        transparent,
        rgba(255, 0, 255, 0.6),
        rgba(255, 0, 255, 0.3),
        transparent);
    animation-duration: 4s;
}

.data-stream:nth-child(3n) {
    background: linear-gradient(to bottom,
        transparent,
        rgba(255, 255, 0, 0.5),
        rgba(255, 255, 0, 0.2),
        transparent);
    animation-duration: 5s;
}

@keyframes stream-flow {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* 增强的全息效果 */
.hologram-enhanced {
    position: relative;
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.1),
        rgba(255, 0, 255, 0.1));
    border: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.hologram-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.1) 2px,
        rgba(0, 255, 255, 0.1) 4px
    );
    animation: hologram-lines 2s linear infinite;
}

@keyframes hologram-lines {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(4px);
    }
}
