/* 太空科技感背景样式 */

/* 主背景类 */
.space-background {
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    background-attachment: fixed;
    position: relative;
    overflow-x: hidden;
}









/* 数字雨效果 */
.digital-rain {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    pointer-events: none;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: rgba(0, 255, 0, 0.3);
    overflow: hidden;
}

.rain-column {
    position: absolute;
    top: -100px;
    animation: rain-fall 8s linear infinite;
}

@keyframes rain-fall {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(100vh);
        opacity: 0;
    }
}

/* 全息投影效果 */
.hologram-effect {
    position: relative;
}

.hologram-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(56, 128, 255, 0.1) 50%,
        transparent 100%
    );
    animation: hologram-scan 3s linear infinite;
    pointer-events: none;
}

@keyframes hologram-scan {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 能量波纹效果 */
.energy-ripple {
    position: absolute;
    border-radius: 50%;
    border: 2px solid rgba(56, 128, 255, 0.6);
    animation: ripple 2s ease-out infinite;
}

@keyframes ripple {
    0% {
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        width: 200px;
        height: 200px;
        opacity: 0;
    }
}

/* 科技感按钮增强 */
.space-btn-enhanced {
    position: relative;
    overflow: hidden;
    background: linear-gradient(45deg, rgba(56, 128, 255, 0.2), rgba(56, 128, 255, 0.4));
    border: 1px solid rgba(56, 128, 255, 0.6);
    box-shadow: 0 0 20px rgba(56, 128, 255, 0.3);
    transition: all 0.3s ease;
}

.space-btn-enhanced:hover {
    box-shadow: 0 0 30px rgba(56, 128, 255, 0.6);
    transform: translateY(-2px);
}

.space-btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.space-btn-enhanced:hover::before {
    left: 100%;
}



/* 增强的全息效果 */
.hologram-enhanced {
    position: relative;
    background: linear-gradient(135deg,
        rgba(0, 255, 255, 0.1),
        rgba(255, 0, 255, 0.1));
    border: 1px solid rgba(0, 255, 255, 0.3);
    box-shadow:
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.hologram-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 2px,
        rgba(0, 255, 255, 0.1) 2px,
        rgba(0, 255, 255, 0.1) 4px
    );
    animation: hologram-lines 2s linear infinite;
}

@keyframes hologram-lines {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(4px);
    }
}
