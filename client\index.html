<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>太空打字挑战赛</title>
    <!-- 预加载关键资源 -->
    <link rel="preload" href="lib/bootstrap.min.css" as="style">
    <link rel="preload" href="css/style.css" as="style">
    <link rel="preload" href="css/space.css" as="style">
    <link rel="preload" href="lib/bootstrap.bundle.min.js" as="script">
    <link rel="preload" href="lib/socket.io.min.js" as="script">
    
    <!-- 加载样式表 -->
    <link href="lib/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/space.css">
    <style>
        /* 虚拟键盘样式 - 更新为更炫酷的样式 */
        .virtual-keyboard {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 20px;
            user-select: none;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(56, 128, 255, 0.2);
        }
        
        .keyboard-row {
            display: flex;
            justify-content: center;
            margin-bottom: 12px;
        }
        
        .key {
            width: 48px;
            height: 48px;
            background: linear-gradient(145deg, #1e2838, #141c28);
            color: #88aadd;
            border-radius: 10px;
            margin: 0 6px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Arial', sans-serif;
            font-weight: bold;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(56, 128, 255, 0.3);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.5), 
                        inset 0 1px 1px rgba(255, 255, 255, 0.1);
            pointer-events: none; /* 禁用点击 */
        }
        
        .key:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 200%;
            height: 100%;
            background: linear-gradient(
                90deg, 
                transparent,
                rgba(56, 128, 255, 0.6),
                transparent
            );
            opacity: 0;
            transition: opacity 0.4s ease;
        }
        
        .key.active {
            background: linear-gradient(145deg, #1a2029, #232d3d);
            box-shadow: 0 0 15px rgba(56, 128, 255, 0.5);
        }
        
        .key.glow:before {
            opacity: 1;
            animation: glow-animation 1s ease-out,
                       running-light 1.5s linear;
        }
        
        @keyframes glow-animation {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 0; }
        }
        
        @keyframes running-light {
            0% {
                left: -100%;
                opacity: 0.8;
            }
            100% {
                left: 100%;
                opacity: 0;
            }
        }
        
        .space {
            width: 240px;
            border-radius: 25px;
            background: linear-gradient(145deg, #1d2631, #17202b);
        }
        
        /* 添加键盘整体发光效果 */
        .virtual-keyboard:after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 16px;
            background: linear-gradient(45deg, 
                rgba(56, 128, 255, 0.1),
                rgba(56, 128, 255, 0),
                rgba(56, 128, 255, 0.1)
            );
            z-index: -1;
            animation: keyboard-glow 3s linear infinite;
        }
        
        @keyframes keyboard-glow {
            0% {
                opacity: 0.3;
            }
            50% {
                opacity: 0.6;
            }
            100% {
                opacity: 0.3;
            }
        }
        
        #app-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            color: #fff;
        }
        
        .app-loading-spinner {
            width: 80px;
            height: 80px;
            border: 8px solid rgba(56, 128, 255, 0.3);
            border-top: 8px solid #3498db;
            border-radius: 50%;
            animation: app-spin 1.5s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes app-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .app-loading-text {
            font-size: 24px;
            margin-top: 20px;
            color: #3498db;
        }
        
        .app-loading-progress {
            width: 300px;
            height: 10px;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 5px;
            margin-top: 20px;
            overflow: hidden;
        }
        
        .app-loading-progress-bar {
            height: 100%;
            width: 0%;
            background-color: #3498db;
            transition: width 0.3s ease;
        }
        
        /* 隐藏主内容，直到加载完成 */
        .main-content {
            display: none;
        }
        
        .app-loaded .main-content {
            display: block;
        }
        
        .app-loaded #app-loading-overlay {
            display: none;
        }
    </style>
</head>
<body class="space-background">
    <!-- 加载指示器 -->
    <div id="app-loading-overlay">
        <div class="app-loading-spinner"></div>
        <div class="app-loading-text">太空打字挑战赛正在启动...</div>
        <div class="app-loading-progress">
            <div id="app-loading-progress-bar" class="app-loading-progress-bar"></div>
        </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">



        
        <div class="container mt-4">
            <!-- 登录界面 -->
            <div id="login-screen" class="card space-card">
                <div class="card-body text-center">
                    <h1 class="space-title mb-4">在线打字挑战赛</h1>
                    <div class="mb-3">
                        <label for="nickname" class="form-label">你的昵称</label>
                        <div class="input-group">
                            <input type="text" class="form-control space-input" id="nickname" readonly>
                            <button class="btn btn-outline-secondary space-btn" type="button" id="generate-nickname">换一个</button>
                        </div>
                    </div>
                    <button id="start-btn" class="btn btn-primary space-btn-primary">开始挑战</button>
                </div>
            </div>
            
            <!-- 等待界面 -->
            <div id="waiting-screen" class="card space-card d-none">
                <div class="card-body text-center">
                    <h2 class="space-title mb-4">等待比赛开始</h2>
                    <div class="loading-animation mb-4">
                        <div class="spinner-border text-light" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <p class="waiting-text">正在等待管理员开始比赛...</p>
                    <p class="user-count">当前在线: <span id="online-count">0</span> 名选手</p>
                </div>
            </div>
            
            <!-- 打字界面 -->
            <div id="typing-screen" class="d-none">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card space-card">
                            <div class="card-body">
                                <div class="typing-header">
                                    <div class="user-info">
                                        <div class="user-nickname-display">宇航员: <span id="user-nickname"></span></div>
                                    </div>
                                    <div class="d-flex justify-content-between mb-3">
                                        <div>
                                            <span class="badge bg-success space-badge">正确: <span id="correct-count">0</span></span>
                                            <span class="badge bg-danger space-badge">错误: <span id="error-count">0</span></span>
                                            <span class="badge bg-info space-badge">准确率: <span id="accuracy">0%</span></span>
                                            <span class="badge bg-warning space-badge">得分: <span id="score">0</span></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="typing-container">
                                    <div id="text-display" class="text-display"></div>
                                    <div class="input-container">
                                        <textarea id="typing-input" class="form-control space-input" 
                                                placeholder="在此输入..." rows="4"></textarea>
                                    </div>
                                    
                                    <!-- 添加屏幕键盘 -->
                                    <div class="virtual-keyboard mt-4">
                                        <div class="keyboard-row">
                                            <div class="key" data-key="q">q</div>
                                            <div class="key" data-key="w">w</div>
                                            <div class="key" data-key="e">e</div>
                                            <div class="key" data-key="r">r</div>
                                            <div class="key" data-key="t">t</div>
                                            <div class="key" data-key="y">y</div>
                                            <div class="key" data-key="u">u</div>
                                            <div class="key" data-key="i">i</div>
                                            <div class="key" data-key="o">o</div>
                                            <div class="key" data-key="p">p</div>
                                        </div>
                                        <div class="keyboard-row">
                                            <div class="key" data-key="a">a</div>
                                            <div class="key" data-key="s">s</div>
                                            <div class="key" data-key="d">d</div>
                                            <div class="key" data-key="f">f</div>
                                            <div class="key" data-key="g">g</div>
                                            <div class="key" data-key="h">h</div>
                                            <div class="key" data-key="j">j</div>
                                            <div class="key" data-key="k">k</div>
                                            <div class="key" data-key="l">l</div>
                                        </div>
                                        <div class="keyboard-row">
                                            <div class="key" data-key="z">z</div>
                                            <div class="key" data-key="x">x</div>
                                            <div class="key" data-key="c">c</div>
                                            <div class="key" data-key="v">v</div>
                                            <div class="key" data-key="b">b</div>
                                            <div class="key" data-key="n">n</div>
                                            <div class="key" data-key="m">m</div>
                                        </div>
                                        <div class="keyboard-row">
                                            <div class="key space" data-key=" ">Space</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 leaderboard-container">
                        <div class="card space-card">
                            <div class="card-header space-card-header">
                                <h5 class="mb-0">宇宙排行榜（前十）</h5>
                            </div>
                            <div class="card-body p-0">
                                <table class="table table-dark table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>排名</th>
                                            <th>宇航员</th>
                                            <th>得分</th>
                                        </tr>
                                    </thead>
                                    <tbody id="leaderboard">
                                        <!-- 排行榜数据将通过JavaScript动态添加 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 结果界面 -->
            <div id="result-screen" class="card space-card d-none">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h2 class="space-title mb-4 text-center">挑战结果</h2>
                            <div class="result-animation mb-4">
                                <img src="img/medal.png" alt="奖牌" class="medal">
                            </div>
                            <div class="result-stats">
                                <p>宇航员: <span id="final-nickname"></span></p>
                                <p>最终得分: <span id="final-score">0</span></p>
                            </div>
                            <div class="text-center">
                                <button id="restart-btn" class="btn btn-primary space-btn-primary mt-3">再来一次</button>
                            </div>
                        </div>
                        
                        <div class="col-md-4 leaderboard-container">
                            <div class="card space-card">
                                <div class="card-header space-card-header">
                                    <h5 class="mb-0">宇宙排行榜（前十）</h5>
                                </div>
                                <div class="card-body p-0">
                                    <table class="table table-dark table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>排名</th>
                                                <th>宇航员</th>
                                                <th>得分</th>
                                            </tr>
                                        </thead>
                                        <tbody id="result-leaderboard">
                                            <!-- 排行榜数据将通过JavaScript动态添加 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 通知提示 -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="notification-toast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">太空控制中心</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="notification-message">
                消息内容
            </div>
        </div>
    </div>
    
    <!-- 内联关键JS以避免额外请求 -->
    <script>
        // 加载进度管理
        let loadedResources = 0;
        const totalResources = 7; // 总资源数量（JS文件数量+1，+1是为了SocketIO连接）
        
        function updateLoadingProgress(increment = 1) {
            loadedResources += increment;
            const progressPercent = Math.min((loadedResources / totalResources) * 100, 100);
            const progressBar = document.getElementById('app-loading-progress-bar');
            if (progressBar) {
                progressBar.style.width = progressPercent + '%';
            }
            
            // 当所有资源加载完成时
            if (loadedResources >= totalResources) {
                setTimeout(() => {
                    document.body.classList.add('app-loaded');
                }, 500);
            }
        }
        
        // 资源加载完成后的回调
        function resourceLoaded() {
            updateLoadingProgress();
        }
        




        // 监听页面加载完成
        window.addEventListener('load', function() {
            // 如果资源已经全部加载，但页面还没显示，则显示页面
            setTimeout(() => {
                if (!document.body.classList.contains('app-loaded')) {
                    document.body.classList.add('app-loaded');
                }
            }, 5000); // 5秒超时，确保页面不会永远卡在加载状态
        });
    </script>
    
    <!-- 脚本加载，添加onload回调 -->
    <script src="lib/bootstrap.bundle.min.js" onload="resourceLoaded()"></script>
    <script src="lib/socket.io.min.js" onload="resourceLoaded()"></script>
    <script src="js/nickname.js" onload="resourceLoaded()"></script>
    <script src="js/socket.js" onload="resourceLoaded()"></script>
    <script src="js/typing.js" onload="resourceLoaded()"></script>
    <script src="js/leaderboard.js" onload="resourceLoaded()"></script>
    <script src="js/main.js" onload="resourceLoaded()"></script>
</body>
</html> 