body {
    font-family: 'Arial', sans-serif;
    color: #fff;
    min-height: 100vh;
    overflow-x: hidden;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    position: relative;
}

/* 科技感背景动画 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.15) 0%, transparent 50%);
    z-index: -2;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* 星空效果 */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: transparent;
}

.stars::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 3s linear infinite;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

/* 闪烁星星 */
.twinkling {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.twinkling::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 25px 25px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 75px 75px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 125px 125px, rgba(255,255,255,0.4), transparent);
    background-repeat: repeat;
    background-size: 150px 150px;
    animation: twinkle 4s linear infinite;
}

@keyframes twinkle {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* 云层效果 */
.clouds {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: transparent;
}

.clouds::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse at center, rgba(255,255,255,0.02) 0%, transparent 70%),
        radial-gradient(ellipse at 30% 20%, rgba(255,255,255,0.01) 0%, transparent 60%),
        radial-gradient(ellipse at 70% 80%, rgba(255,255,255,0.015) 0%, transparent 50%);
    animation: drift 30s linear infinite;
}

@keyframes drift {
    from { transform: translateX(-100px); }
    to { transform: translateX(100px); }
}

.space-card {
    background: linear-gradient(135deg, rgba(25, 32, 71, 0.9), rgba(37, 45, 90, 0.8));
    border: 1px solid rgba(83, 109, 254, 0.5);
    border-radius: 15px;
    box-shadow:
        0 0 30px rgba(83, 109, 254, 0.4),
        inset 0 1px 1px rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.space-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(83, 109, 254, 0.1) 0%,
        transparent 50%,
        rgba(83, 109, 254, 0.1) 100%);
    border-radius: 15px;
    z-index: -1;
    animation: card-glow 4s ease-in-out infinite;
}

@keyframes card-glow {
    0%, 100% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.7;
    }
}

.space-card-header {
    background-color: rgba(37, 45, 90, 0.8);
    border-bottom: 1px solid rgba(83, 109, 254, 0.5);
    color: #fff;
}

.space-title {
    color: #fff;
    text-shadow: 0 0 10px rgba(83, 109, 254, 0.8);
    font-weight: bold;
}

.space-input {
    background-color: rgba(25, 32, 71, 0.6);
    border: 1px solid rgba(83, 109, 254, 0.5);
    color: #fff;
}

.space-input:focus {
    background-color: rgba(37, 45, 90, 0.8);
    border-color: rgba(83, 109, 254, 0.8);
    box-shadow: 0 0 0 0.25rem rgba(83, 109, 254, 0.25);
    color: #fff;
}

.space-btn {
    border-color: rgba(83, 109, 254, 0.5);
    color: rgba(83, 109, 254, 1);
}

.space-btn:hover {
    background-color: rgba(83, 109, 254, 0.2);
    border-color: rgba(83, 109, 254, 0.8);
    color: #fff;
}

.space-btn-primary {
    background: linear-gradient(135deg, rgba(83, 109, 254, 0.8), rgba(120, 119, 198, 0.8));
    border: 1px solid rgba(83, 109, 254, 0.6);
    color: #fff;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(83, 109, 254, 0.4);
    transition: all 0.3s ease;
}

.space-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.space-btn-primary:hover {
    background: linear-gradient(135deg, rgba(83, 109, 254, 1), rgba(120, 119, 198, 1));
    border-color: rgba(83, 109, 254, 0.8);
    box-shadow: 0 0 30px rgba(83, 109, 254, 0.6);
    transform: translateY(-2px);
}

.space-btn-primary:hover::before {
    left: 100%;
}

.space-btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 0 15px rgba(83, 109, 254, 0.8);
}

.space-badge {
    background-color: rgba(37, 45, 90, 0.8);
    border: 1px solid rgba(83, 109, 254, 0.3);
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
    color: #3498db !important;
    font-weight: bold;
}

.typing-container {
    position: relative;
}

.text-display {
    background-color: rgba(37, 45, 90, 0.5);
    border-radius: 5px;
    padding: 20px;
    font-size: 1.5rem;
    line-height: 1.8;
    height: 200px;  /* 固定高度 */
    margin-bottom: 20px;
    font-family: 'Courier New', monospace;
    position: relative;
    overflow: hidden;  /* 防止内容溢出 */
}

.text-display .correct {
    color: #28a745;
}

.text-display .incorrect {
    color: #ff4444;
}

.text-display .current {
    background-color: rgba(83, 109, 254, 0.3);
    border-radius: 2px;
}

.text-display small {
    font-size: 0.9rem;
    display: block;
    margin-top: 10px;
    color: #adb5bd;
}

.input-container {
    margin-top: 20px;
}

.typing-container .input-container {
    margin-top: 20px;
}

.typing-container .space-input {
    height: 100px;  /* 减小输入框高度 */
    font-size: 1.5rem;
    line-height: 1.8;
    padding: 15px;
    resize: none;
}

.status-message {
    font-size: 0.9rem;
    color: #adb5bd;
    text-align: center;
}

.rocket-animation {
    animation: rocket-float 3s ease-in-out infinite;
}

.rocket {
    width: 100px;
    height: auto;
}

@keyframes rocket-float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0);
    }
}

.medal {
    width: 120px;
    height: auto;
    animation: medal-shine 2s ease-in-out infinite;
}

@keyframes medal-shine {
    0% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.3);
    }
    100% {
        filter: brightness(1);
    }
}

.waiting-text {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.user-count {
    font-size: 1rem;
    color: #adb5bd;
}

table.table-dark {
    background-color: transparent;
}

table.table-dark thead th {
    background-color: rgba(37, 45, 90, 0.8);
    border-color: rgba(83, 109, 254, 0.3);
}

table.table-dark tbody td {
    background-color: rgba(25, 32, 71, 0.5);
    border-color: rgba(83, 109, 254, 0.2);
}

table.table-dark tbody tr:hover td {
    background-color: rgba(37, 45, 90, 0.7);
}

/* 排行榜前三名特殊样式 */
.leaderboard-rank-1 {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.4), rgba(255, 223, 0, 0.2)) !important;
    border: 2px solid rgba(255, 215, 0, 0.8) !important;
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.6),
        inset 0 1px 1px rgba(255, 255, 255, 0.3) !important;
    position: relative;
    animation: champion-pulse 3s ease-in-out infinite;
}

.leaderboard-rank-1::before {
    content: '👑';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 22px;
    animation: crown-glow 2s ease-in-out infinite;
    filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.8));
}

@keyframes champion-pulse {
    0%, 100% {
        box-shadow:
            0 0 20px rgba(255, 215, 0, 0.6),
            inset 0 1px 1px rgba(255, 255, 255, 0.3);
    }
    50% {
        box-shadow:
            0 0 30px rgba(255, 215, 0, 0.8),
            inset 0 1px 1px rgba(255, 255, 255, 0.5);
    }
}

.leaderboard-rank-2 {
    background: linear-gradient(135deg, rgba(192, 192, 192, 0.4), rgba(192, 192, 192, 0.2)) !important;
    border: 2px solid rgba(192, 192, 192, 0.7) !important;
    box-shadow:
        0 0 15px rgba(192, 192, 192, 0.5),
        inset 0 1px 1px rgba(255, 255, 255, 0.2) !important;
    position: relative;
    animation: silver-glow 3s ease-in-out infinite;
}

.leaderboard-rank-2::before {
    content: '🥈';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    animation: medal-shine 2s ease-in-out infinite;
    filter: drop-shadow(0 0 3px rgba(192, 192, 192, 0.8));
}

.leaderboard-rank-3 {
    background: linear-gradient(135deg, rgba(205, 127, 50, 0.4), rgba(205, 127, 50, 0.2)) !important;
    border: 2px solid rgba(205, 127, 50, 0.7) !important;
    box-shadow:
        0 0 12px rgba(205, 127, 50, 0.5),
        inset 0 1px 1px rgba(255, 255, 255, 0.2) !important;
    position: relative;
    animation: bronze-glow 3s ease-in-out infinite;
}

.leaderboard-rank-3::before {
    content: '🥉';
    position: absolute;
    left: -15px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    animation: medal-shine 2s ease-in-out infinite;
    filter: drop-shadow(0 0 3px rgba(205, 127, 50, 0.8));
}

@keyframes silver-glow {
    0%, 100% {
        box-shadow:
            0 0 15px rgba(192, 192, 192, 0.5),
            inset 0 1px 1px rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow:
            0 0 20px rgba(192, 192, 192, 0.7),
            inset 0 1px 1px rgba(255, 255, 255, 0.3);
    }
}

@keyframes bronze-glow {
    0%, 100% {
        box-shadow:
            0 0 12px rgba(205, 127, 50, 0.5),
            inset 0 1px 1px rgba(255, 255, 255, 0.2);
    }
    50% {
        box-shadow:
            0 0 18px rgba(205, 127, 50, 0.7),
            inset 0 1px 1px rgba(255, 255, 255, 0.3);
    }
}

@keyframes crown-glow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(255, 215, 0, 0.8);
        transform: translateY(-50%) scale(1);
    }
    50% {
        text-shadow: 0 0 15px rgba(255, 215, 0, 1);
        transform: translateY(-50%) scale(1.1);
    }
}

/* 排行榜第一名昵称特殊样式 */
.leaderboard-rank-1 .leaderboard-nickname {
    color: #ffd700 !important;
    font-weight: 800 !important;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.8) !important;
    animation: gold-text-glow 2s ease-in-out infinite;
}

.leaderboard-rank-2 .leaderboard-nickname {
    color: #c0c0c0 !important;
    font-weight: 700 !important;
    text-shadow: 0 0 8px rgba(192, 192, 192, 0.8) !important;
}

.leaderboard-rank-3 .leaderboard-nickname {
    color: #cd7f32 !important;
    font-weight: 700 !important;
    text-shadow: 0 0 8px rgba(205, 127, 50, 0.8) !important;
}

@keyframes gold-text-glow {
    0%, 100% {
        text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);
    }
    50% {
        text-shadow: 0 0 20px rgba(255, 215, 0, 1), 0 0 30px rgba(255, 215, 0, 0.5);
    }
}

/* 排行榜容器增强 */
.leaderboard-container .space-card {
    position: relative;
    overflow: visible;
}

.leaderboard-container .table {
    position: relative;
}

/* 排行榜标题增强 */
.leaderboard-container .space-card-header h5 {
    background: linear-gradient(45deg, #4dabf7, #74c0fc, #91d5ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: none;
    font-weight: 800;
    animation: title-glow 3s ease-in-out infinite;
    position: relative;
}

.leaderboard-container .space-card-header h5::before {
    content: '🚀';
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    animation: rocket-float 3s ease-in-out infinite;
}

@keyframes title-glow {
    0%, 100% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.3);
    }
}

/* 排行榜数据加载动画 */
.leaderboard-loading {
    position: relative;
    overflow: hidden;
}

.leaderboard-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(56, 128, 255, 0.3), transparent);
    animation: loading-sweep 1.5s ease-in-out infinite;
}

@keyframes loading-sweep {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 排行榜行动画 */
.leaderboard-row-enter {
    animation: row-slide-in 0.5s ease-out forwards;
    opacity: 0;
    transform: translateX(-20px);
}

@keyframes row-slide-in {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 排行榜得分数字动画 */
.score-animate {
    animation: score-count-up 1s ease-out;
}

@keyframes score-count-up {
    from {
        transform: scale(0.8);
        opacity: 0.5;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* 排行榜容器边框动画 */
.leaderboard-container .space-card {
    border: 1px solid rgba(83, 109, 254, 0.5);
    position: relative;
    overflow: hidden;
}

.leaderboard-container .space-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(56, 128, 255, 0.8), transparent);
    animation: border-scan 3s linear infinite;
}

/* 排行榜更新闪烁效果 */
.leaderboard-update-flash {
    animation: update-flash 0.8s ease-out;
}

@keyframes update-flash {
    0% {
        box-shadow: 0 0 0 rgba(56, 128, 255, 0.8);
    }
    50% {
        box-shadow: 0 0 30px rgba(56, 128, 255, 0.8);
    }
    100% {
        box-shadow: 0 0 0 rgba(56, 128, 255, 0.8);
    }
}

/* 新记录提示动画 */
.new-record {
    position: relative;
    animation: new-record-celebration 2s ease-out;
}

@keyframes new-record-celebration {
    0% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.05);
        box-shadow: 0 0 40px rgba(255, 215, 0, 0.8);
    }
    50% {
        transform: scale(1.02);
    }
    75% {
        transform: scale(1.05);
        box-shadow: 0 0 40px rgba(255, 215, 0, 0.8);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes border-scan {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 排行榜表格增强效果 */
.leaderboard-container table {
    position: relative;
    z-index: 1;
}

.leaderboard-container tbody tr {
    transition: all 0.3s ease;
    position: relative;
}

.leaderboard-container tbody tr:hover {
    transform: translateX(5px);
    box-shadow: -5px 0 15px rgba(56, 128, 255, 0.3);
}

/* 数据传输效果 */
.data-transfer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(0, 255, 255, 0.3) 25%,
        rgba(0, 255, 255, 0.6) 50%,
        rgba(0, 255, 255, 0.3) 75%,
        transparent 100%);
    animation: data-flow 1.5s ease-in-out;
    pointer-events: none;
    z-index: 10;
}

@keyframes data-flow {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 排行榜标题科技感增强 */
.leaderboard-container .space-card-header {
    position: relative;
    overflow: hidden;
}

.leaderboard-container .space-card-header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(0, 255, 255, 0.8),
        transparent);
    animation: header-scan 4s linear infinite;
}

@keyframes header-scan {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* 排行榜数字跳动效果 */
.score-jump {
    animation: score-jump 0.6s ease-out;
}

@keyframes score-jump {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
        color: #00ffff;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
    }
    100% {
        transform: scale(1);
    }
}

/* 昵称显示样式 */
#nickname {
    color: #3498db;
    font-weight: bold;
}

/* 添加结果界面的样式 */
.result-stats {
    font-size: 1.5rem;
    margin: 20px 0;
    padding: 20px;
    background-color: rgba(37, 45, 90, 0.5);
    border-radius: 10px;
}

.result-stats p {
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    padding: 0 50px;
}

.result-stats span {
    font-weight: bold;
    color: #3498db;
}

/* 修改文本显示区域宽度 */
.col-md-8 {
    width: 70%;  /* 增加主要内容区域的宽度 */
}

.col-md-4 {
    width: 30%;  /* 减小排行榜区域的宽度 */
}

.current-line {
    background-color: rgba(83, 109, 254, 0.3);  /* 增加背景色透明度 */
    padding: 8px;  /* 增加内边距 */
    border-radius: 5px;
    margin-bottom: 10px;
    border-left: 4px solid #3498db;  /* 添加左边框 */
    box-shadow: 0 0 5px rgba(83, 109, 254, 0.5);  /* 添加发光效果 */
}

.other-line {
    color: #95a5a6;  /* 调整其他行的颜色 */
    margin-bottom: 10px;
    padding: 8px;
    opacity: 0.7;  /* 降低其他行的不透明度 */
}

.page-info {
    position: absolute;
    bottom: 10px;
    right: 20px;
    font-size: 0.8rem;
    color: #95a5a6;
}

/* 修改结果界面的样式 */
#result-screen .card-body {
    padding: 30px;
}

#result-screen .result-stats {
    background-color: rgba(37, 45, 90, 0.5);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

#result-screen .result-stats p {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    font-size: 1.2rem;
}

#result-screen .result-stats span {
    color: #3498db;
    font-weight: bold;
}

#result-screen .medal {
    width: 120px;
    height: auto;
    margin: 0 auto;
    display: block;
}

/* 结果界面的排行榜样式 */
#result-screen .table {
    margin-bottom: 0;
}

#result-screen .table th,
#result-screen .table td {
    padding: 12px;
    text-align: center;
}

/* 添加昵称显示样式 */
.user-nickname-display {
    font-size: 1.25rem;
    font-weight: 600;
    color: #4dabf7;
    text-shadow: 0 0 10px rgba(77, 171, 247, 0.5);
    background-color: rgba(0, 0, 0, 0.3);
    padding: 8px 15px;
    border-radius: 10px;
    border: 1px solid rgba(77, 171, 247, 0.3);
    display: inline-block;
    margin-bottom: 15px;
}

/* 排行榜中的昵称样式 */
.leaderboard-nickname {
    font-weight: 600;
    color: #4dabf7;
}

/* 完成后的消息样式 */
.completed-message {
    color: #28a745;
    font-size: 1.5rem;
    text-align: center;
    padding: 20px;
    margin-top: 20px;
    text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.waiting-message {
    margin-top: 15px;
    text-align: center;
    font-weight: 500;
}

.waiting-message .spinner-border {
    margin-left: 10px;
}

/* 启动动画增强 */
.main-content {
    animation: app-startup 1.5s ease-out;
}

@keyframes app-startup {
    0% {
        opacity: 0;
        transform: scale(0.95);
        filter: blur(5px);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.02);
    }
    100% {
        opacity: 1;
        transform: scale(1);
        filter: blur(0);
    }
}

/* 科技感文字效果 */
.space-title {
    position: relative;
    background: linear-gradient(45deg, #4dabf7, #74c0fc, #91d5ff, #4dabf7);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: title-gradient 3s ease-in-out infinite;
}

@keyframes title-gradient {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* 增强的加载指示器 */
#app-loading-overlay {
    background: linear-gradient(135deg, #000000, #1a1a2e, #16213e);
    animation: loading-bg 2s ease-in-out infinite;
}

@keyframes loading-bg {
    0%, 100% {
        background: linear-gradient(135deg, #000000, #1a1a2e, #16213e);
    }
    50% {
        background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
    }
}

.app-loading-spinner {
    border: 8px solid rgba(56, 128, 255, 0.3);
    border-top: 8px solid #3498db;
    border-right: 8px solid #00ffff;
    box-shadow: 0 0 30px rgba(56, 128, 255, 0.5);
}

.app-loading-text {
    text-shadow: 0 0 10px rgba(52, 152, 219, 0.8);
    animation: text-pulse 2s ease-in-out infinite;
}

@keyframes text-pulse {
    0%, 100% {
        text-shadow: 0 0 10px rgba(52, 152, 219, 0.8);
    }
    50% {
        text-shadow: 0 0 20px rgba(52, 152, 219, 1), 0 0 30px rgba(0, 255, 255, 0.5);
    }
}